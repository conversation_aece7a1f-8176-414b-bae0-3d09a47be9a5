<script setup>

defineProps({
  num: {
    type: String,
    default: ''
  }
})

</script>

<template>
  <div class="con">
    <div class="box">
      <div class="desc">相似度</div>
      <div class="num">{{num}}%</div>
    </div>
  </div>
</template>

<style scoped>
.con {
  width: 100vw;
  height: 100vh;
  background-color: #0e375c;
}
.box {
  width: 70px;
  height: 48px;
  background: linear-gradient( 180deg, rgba(5,74,128,0) 0%, #0D7BCB 100%);
  border-radius: 0px 0px 0px 0px;
  border: 2px solid;
  border-image: linear-gradient(180deg, rgba(29.000000171363354, 160.00000566244125, 255, 0), rgba(29.000000171363354, 160.00000566244125, 255, 0), rgba(29.000000171363354, 160.00000566244125, 255, 1)) 2 2;
}
.desc {
  font-size: 14px;
  color: #89CFFF;
}
.num {
  color: #fff;
  font-size: 20px;
  font-weight: 500;
}
.a {
  color: red;
}
</style>
