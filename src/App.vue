<script setup>
import demo1 from "./components/demo1.vue";
import demo2 from "./components/demo2";
import HelloWorld from "./components/HelloWorld.vue";

const options = [
  {
    label: 1,
    value: 1,
  },
  {
    label: 2,
    value: 2,
  },
];

const handleChange = (a, b) => {
  console.log("a,b======>", a, b);
};
</script>

<template>
  <div>
    <!-- <demo1 /> -->
    <HelloWorld />
  </div>
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
