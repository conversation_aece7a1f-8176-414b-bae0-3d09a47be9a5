<template>
  <div class="box">
    <el-space :size="50">
      <el-space>
        <el-text>添加路径</el-text>
        <el-icon><Minus /></el-icon>
      </el-space>
      <el-space>
        <el-text>添加出入口</el-text>
        <el-icon><Location /></el-icon>
      </el-space>
    </el-space>
    <div class="w-full flex justify-center mt-20">
      <div ref="imgBox" class="w-900 relative canvasBox">
        <el-image
          :src="testPng"
          class="w-full h-auto select-none z-1"
          fit="fill"
          @load="onImgResize"
        />
        <canvas
          ref="cv"
          class="canvas"
          @mousedown="start"
          @mousemove="move"
          @mouseup="end"
          @mouseleave="leave"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import testPng from "../assets/test.png";

const props = defineProps({
  imageUrl: { type: String, default: "" },
});

const cv = ref();
const ctx = ref();
const imgBox = ref();

const cur = ref(null); // 正在画的线
const lines = ref([]); // 已保留线段 {x1,y1,x2,y2,color}
const hoverIdx = ref(-1); // 命中线段索引
let drawing = false; // 画线 or 删除

/* ---------- 生命周期 ---------- */
onMounted(() => {
  ctx.value = cv.value.getContext("2d");
  window.addEventListener("resize", onImgResize);
  window.addEventListener("keydown", handleKey);
});

onUnmounted(() => {
  window.removeEventListener("resize", onImgResize);
  window.removeEventListener("keydown", handleKey);
});

/* ---------- 画布尺寸 = 图片渲染尺寸 ---------- */
function onImgResize() {
  nextTick(() => {
    const imgEl = imgBox.value?.querySelector("img");
    if (!imgEl) return;
    const w = imgEl.clientWidth;
    const h = imgEl.clientHeight;
    setCanvasSize(w, h);
  });
}

function setCanvasSize(w, h) {
  const dpr = window.devicePixelRatio || 1;
  // 物理像素
  cv.value.width = w * dpr;
  cv.value.height = h * dpr;
  // CSS 尺寸
  cv.value.style.width = w + "px";
  cv.value.style.height = h + "px";
  // 坐标系缩放
  ctx.value.setTransform(dpr, 0, 0, dpr, 0, 0);
  redraw();
}

/* ---------- 鼠标事件 ---------- */
function start(e) {
  const rect = cv.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  const idx = hitTest(x, y);
  if (idx >= 0) {
    lines.value.splice(idx, 1);
    drawing = false;
    redraw();
  } else {
    drawing = true;
    cur.value = { x1: x, y1: y, x2: x, y2: y };
  }
}

function move(e) {
  const rect = cv.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  if (drawing) {
    cur.value.x2 = x;
    cur.value.y2 = y;
  } else {
    hoverIdx.value = hitTest(x, y);
  }
  redraw();
}

function end() {
  if (!drawing) return;
  drawing = false;
  const dx = cur.value.x2 - cur.value.x1;
  const dy = cur.value.y2 - cur.value.y1;
  if (Math.hypot(dx, dy) > 3) {
    lines.value.push({ ...cur.value, color: "#ff0000" });
  }
  cur.value = null;
  redraw();
}

function leave() {
  hoverIdx.value = -1;
  redraw();
}

/* ---------- 撤销 ---------- */
function handleKey(e) {
  if (e.ctrlKey && e.key === "z") {
    e.preventDefault();
    lines.value.pop();
    redraw();
  }
}

/* ---------- 命中检测 ---------- */
function hitTest(px, py) {
  for (let i = 0; i < lines.value.length; i++) {
    const l = lines.value[i];
    if (distToSegment(px, py, l.x1, l.y1, l.x2, l.y2) < 5) return i;
  }
  return -1;
}

function distToSegment(px, py, x1, y1, x2, y2) {
  const A = px - x1,
    B = py - y1,
    C = x2 - x1,
    D = y2 - y1;
  const dot = A * C + B * D,
    lenSq = C * C + D * D;
  let param = -1;
  if (lenSq !== 0) param = dot / lenSq;
  let xx, yy;
  param < 0
    ? ((xx = x1), (yy = y1))
    : param > 1
    ? ((xx = x2), (yy = y2))
    : ((xx = x1 + param * C), (yy = y1 + param * D));
  return Math.hypot(px - xx, py - yy);
}

/* ---------- 绘制 ---------- */
function redraw() {
  ctx.value.clearRect(0, 0, cv.value.width, cv.value.height);
  lines.value.forEach((l, i) =>
    drawLine(
      l,
      i === hoverIdx.value ? "#ff4d4f" : l.color,
      i === hoverIdx.value ? 3 : 2
    )
  );
  if (cur.value) drawLine(cur.value, "#000", 2);
}

function drawLine({ x1, y1, x2, y2 }, color, width) {
  ctx.value.beginPath();
  ctx.value.moveTo(x1, y1);
  ctx.value.lineTo(x2, y2);
  ctx.value.strokeStyle = color;
  ctx.value.lineWidth = width;
  ctx.value.stroke();
}
</script>

<style scoped>
.box {
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;
}
.canvasBox {
  max-width: 900px;
  width: 100%;
  border: 1px solid #ddd;
}
.canvas {
  position: absolute;
  inset: 0;
  z-index: 10;
  cursor: crosshair;
  pointer-events: all;
}
</style>
